import { FrontendLogger } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const deleteResponseApi = async (responseId: string) => {
  try {
    const result = await ApiWrapper(`/responses/${responseId}`, {
      method: 'DELETE',
      body: {},
      includeCsrf: true, // Include CSRF token for authenticated requests
    });

    // DEBUG: Log response deletion result
    FrontendLogger.debug('Response deletion completed', {
      responseId,
      success: result.success,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log response deletion error
    FrontendLogger.error('Error deleting response', {
      responseId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });
    return {
      success: false,
      message: 'Error deleting response',
      payload: null,
    };
  }
};
